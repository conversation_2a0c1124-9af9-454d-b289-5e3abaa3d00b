# 个人博客网站开发计划

## 项目概述
创建一个个人博客网站作为主页，通过链接跳转到其他功能性网站。

## 技术栈选择

### 前端技术
- **框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **UI框架**: Element Plus 或 Naive UI
- **样式**: Tailwind CSS + SCSS
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **HTTP客户端**: Axios

### 后端技术
- **语言**: Java 17+
- **框架**: Spring Boot 3.x
- **安全**: Spring Security
- **数据访问**: MyBatis Plus 或 Spring Data JPA
- **API文档**: Swagger/OpenAPI 3
- **缓存**: Redis（可选）

### 数据库
- **主数据库**: MySQL 8.0+
- **连接池**: HikariCP

### 部署环境
- **服务器**: 宝塔面板
- **Web服务器**: Nginx
- **应用服务器**: Tomcat（内嵌在Spring Boot中）

## 项目结构规划

```
personal-blog/
├── frontend/                 # Vue3前端项目
│   ├── src/
│   │   ├── components/       # 公共组件
│   │   ├── views/           # 页面组件
│   │   ├── router/          # 路由配置
│   │   ├── stores/          # Pinia状态管理
│   │   ├── api/             # API接口
│   │   ├── utils/           # 工具函数
│   │   └── assets/          # 静态资源
│   ├── public/
│   └── package.json
├── backend/                  # Spring Boot后端项目
│   ├── src/main/java/
│   │   └── com/blog/
│   │       ├── controller/   # 控制器
│   │       ├── service/      # 业务逻辑
│   │       ├── mapper/       # 数据访问层
│   │       ├── entity/       # 实体类
│   │       ├── dto/          # 数据传输对象
│   │       ├── config/       # 配置类
│   │       └── utils/        # 工具类
│   ├── src/main/resources/
│   │   ├── mapper/           # MyBatis XML文件
│   │   └── application.yml   # 配置文件
│   └── pom.xml
└── database/                 # 数据库脚本
    ├── init.sql             # 初始化脚本
    └── data.sql             # 测试数据
```

## 开发步骤

### 阶段一：环境搭建和项目初始化
1. **创建前端项目**
   ```bash
   npm create vue@latest frontend
   cd frontend
   npm install
   npm install element-plus axios pinia vue-router@4
   npm install -D tailwindcss postcss autoprefixer
   ```

2. **创建后端项目**
   - 使用Spring Initializr创建Spring Boot项目
   - 添加依赖：Web, MySQL Driver, MyBatis Plus, Spring Security

3. **数据库设计**
   - 设计用户表、文章表、分类表、标签表等
   - 创建数据库初始化脚本

### 阶段二：后端API开发
1. **基础配置**
   - 配置数据库连接
   - 配置MyBatis Plus
   - 配置跨域处理
   - 配置Swagger文档

2. **核心功能开发**
   - 用户管理（注册、登录、权限）
   - 文章管理（CRUD操作）
   - 分类和标签管理
   - 文件上传功能

3. **API接口**
   - RESTful API设计
   - 统一返回格式
   - 异常处理
   - 参数验证

### 阶段三：前端页面开发
1. **项目配置**
   - 配置路由
   - 配置Axios拦截器
   - 配置Pinia状态管理
   - 配置Tailwind CSS

2. **页面组件开发**
   - 首页（个人介绍、最新文章）
   - 文章列表页
   - 文章详情页
   - 关于我页面
   - 项目展示页
   - 功能网站导航页

3. **公共组件**
   - 导航栏
   - 侧边栏
   - 文章卡片
   - 分页组件
   - 加载组件

### 阶段四：功能完善
1. **响应式设计**
   - 移动端适配
   - 平板端适配
   - 桌面端优化

2. **用户体验优化**
   - 加载动画
   - 错误处理
   - 搜索功能
   - 评论系统（可选）

3. **SEO优化**
   - Meta标签优化
   - 结构化数据
   - 站点地图

### 阶段五：部署和上线
1. **前端构建**
   ```bash
   npm run build
   ```

2. **后端打包**
   ```bash
   mvn clean package
   ```

3. **宝塔部署**
   - 配置MySQL数据库
   - 部署Spring Boot应用
   - 配置Nginx反向代理
   - 配置域名和SSL证书

## 功能模块详细规划

### 1. 首页模块
- 个人头像和简介
- 技能标签云
- 最新文章展示
- 功能网站快速导航
- 联系方式

### 2. 文章模块
- 文章列表（支持分页、分类筛选）
- 文章详情（支持Markdown渲染）
- 文章搜索
- 标签系统

### 3. 项目展示模块
- 项目卡片展示
- 项目详情页
- 技术栈标签
- 项目链接跳转

### 4. 关于我模块
- 详细个人介绍
- 教育经历
- 工作经验
- 技能树

### 5. 导航模块
- 功能网站列表
- 分类导航
- 外链跳转

## 数据库设计

### 主要数据表
1. **用户表 (users)**
   - id, username, password, email, avatar, created_at, updated_at

2. **文章表 (articles)**
   - id, title, content, summary, cover_image, category_id, author_id, status, created_at, updated_at

3. **分类表 (categories)**
   - id, name, description, sort_order

4. **标签表 (tags)**
   - id, name, color

5. **文章标签关联表 (article_tags)**
   - article_id, tag_id

6. **项目表 (projects)**
   - id, name, description, image, url, tech_stack, sort_order

7. **功能网站表 (function_sites)**
   - id, name, description, url, icon, category, sort_order

## 开发时间估算
- 环境搭建：1-2天
- 后端开发：3-5天
- 前端开发：4-6天
- 测试和优化：2-3天
- 部署上线：1天

**总计：11-17天**

## 注意事项
1. 确保宝塔面板已安装Java环境和MySQL
2. 注意跨域配置和安全设置
3. 定期备份数据库
4. 监控服务器性能
5. 考虑CDN加速静态资源

## 下一步行动
1. 首先创建前端Vue3项目
2. 创建后端Spring Boot项目
3. 设计并创建MySQL数据库表结构
4. 开发后端API接口
5. 开发前端页面组件
6. 集成测试
7. 部署到宝塔面板

## 快速开始命令

### 创建前端项目
```bash
npm create vue@latest frontend
cd frontend
npm install
npm install element-plus axios pinia
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p
```

### 创建后端项目
访问 https://start.spring.io/ 创建Spring Boot项目，或使用IDE的Spring Initializr插件

### 数据库初始化
```sql
CREATE DATABASE personal_blog CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

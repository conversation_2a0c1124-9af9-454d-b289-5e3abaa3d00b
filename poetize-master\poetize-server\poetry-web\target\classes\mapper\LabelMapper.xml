<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ld.poetry.dao.LabelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ld.poetry.entity.Label">
        <id column="id" property="id"/>
        <result column="sort_id" property="sortId"/>
        <result column="label_name" property="labelName"/>
        <result column="label_description" property="labelDescription"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sort_id, label_name, label_description
    </sql>

</mapper>

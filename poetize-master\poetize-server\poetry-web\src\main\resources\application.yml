server:
  port: 8081
  tomcat:
    threads.max: 50
    max-connections: 100
    max-http-form-post-size: 120MB
    connection-timeout: 60000

mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-value: 1
      logic-not-delete-value: 0

spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

  mail:
    host: smtp.qq.com
    username:
    password:
    default-encoding: utf-8
    protocol: smtp
    port: 465
    properties:
      mail:
        smtp:
          socketFactory:
            port: 465
            class: javax.net.ssl.SSLSocketFactory
          ssl:
            enable: true

  datasource:
    username: root
    password: 331307
    url: **************************************************************************************************************************************************
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver

user:
  code:
    format:
  subscribe:
    format:

store:
  type:

local:
  enable:
  uploadUrl:
  downloadUrl:

qiniu:
  enable:
  accessKey:
  secretKey:
  bucket:
  downloadUrl:

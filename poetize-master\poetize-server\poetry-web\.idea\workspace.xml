<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.projectsettings.compiler.javacompiler&quot;
  }
}</component>
  <component name="RunManager" selected="Spring Boot.PoetryApplication">
    <configuration name="PoetryApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.ld.poetry.PoetryApplication" />
      <module name="poetry-web" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ld.poetry.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PoetryApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="poetry-web" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ld.poetry.PoetryApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.PoetryApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <created>1730207202165</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1730207202165</updated>
      <workItem from="1730207203499" duration="155000" />
    </task>
    <servers />
  </component>
</project>
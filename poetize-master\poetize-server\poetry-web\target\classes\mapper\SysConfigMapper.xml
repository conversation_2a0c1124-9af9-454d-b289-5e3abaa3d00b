<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ld.poetry.dao.SysConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ld.poetry.entity.SysConfig">
        <id column="id" property="id" />
        <result column="config_name" property="configName" />
        <result column="config_key" property="configKey" />
        <result column="config_value" property="configValue" />
        <result column="config_type" property="configType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, config_name, config_key, config_value, config_type
    </sql>

</mapper>
